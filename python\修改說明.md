# 大專院校爬蟲.py 修改說明

## 修改概述
根據用戶要求，將原本下載XLSX格式的爬蟲程序修改為下載CSV格式，並解決UTF-8編碼亂碼問題。

## 主要修改內容

### 1. 函數名稱修改
- `尋找XLSX下載連結` → `尋找CSV下載連結`
- `下載XLSX檔案` → `下載CSV檔案`

### 2. 下載邏輯修改

#### 原始邏輯 (XLSX)
```python
# 在這個<tr>中尋找XLSX下載連結
xlsx_link = tr_element.find('a', href=lambda href: href and '.xlsx' in href)
```

#### 修改後邏輯 (CSV)
```python
# 在這個<tr>中尋找CSV下載連結
csv_link = tr_element.find('a', href=lambda href: href and '.csv' in href)
```

### 3. 編碼處理改進

#### 原始下載方式
```python
with open(output_path, 'wb') as f:
    f.write(response.content)
```

#### 修改後下載方式
```python
# 先將內容解碼為UTF-8，然後重新編碼為帶BOM的UTF-8
try:
    # 嘗試以UTF-8解碼
    content_text = response.content.decode('utf-8')
except UnicodeDecodeError:
    # 如果UTF-8解碼失敗，嘗試其他編碼
    try:
        content_text = response.content.decode('utf-8-sig')
    except UnicodeDecodeError:
        content_text = response.content.decode('big5')

# 寫入檔案時使用帶BOM的UTF-8編碼
with open(output_path, 'w', encoding='utf-8-sig', newline='') as f:
    f.write(content_text)
```

### 4. 檔案副檔名修改
- 輸出檔案從 `.xlsx` 改為 `.csv`
- 相關變數名稱從 `xlsx_url` 改為 `csv_url`

### 5. 錯誤處理更新
- 增加 `UnicodeDecodeError` 異常處理
- 更新錯誤訊息中的檔案格式描述

## 編碼解決方案詳解

### 問題描述
下載的CSV檔案是UTF-8格式，但在Windows系統中打開會顯示亂碼。

### 解決方案
1. **多重編碼嘗試**: 依序嘗試 `utf-8` → `utf-8-sig` → `big5` 編碼解碼
2. **BOM標記**: 使用 `utf-8-sig` 編碼寫入檔案，自動添加BOM (Byte Order Mark)
3. **換行處理**: 使用 `newline=''` 參數避免額外的換行符問題

### BOM的作用
- BOM (Byte Order Mark) 是一個特殊的Unicode字符
- 在檔案開頭添加BOM可以讓Windows系統正確識別UTF-8編碼
- `utf-8-sig` 編碼會自動在檔案開頭添加UTF-8 BOM (EF BB BF)

## HTML結構對應

根據提供的HTML結構，程序會尋找以下格式的CSV下載連結：

```html
<td headers="csv" class="text-center">
    <a class="btn btn-warning" href="/download/udata/static_file/財1-1.國立學校可用資金、本年度現金增減情形-以「校」統計.csv" target="_blank" title="財1-1.國立學校可用資金、本年度現金增減情形-以「校」統計.csv [下載 匯出為csv檔案格式]">
        <i class="glyphicon glyphicon-download-alt" aria-hidden="true"></i> .csv
    </a>
</td>
```

## 測試建議

1. **網絡連接測試**: 確保能正常訪問 `https://udb.moe.edu.tw`
2. **CSV連結驗證**: 檢查目標頁面是否包含CSV下載連結
3. **編碼驗證**: 下載後的CSV檔案在Excel中打開應該不會有亂碼
4. **檔案完整性**: 確認下載的CSV檔案內容完整且格式正確

## 注意事項

1. **網絡穩定性**: 如果網絡不穩定，可能需要調整重試次數或延遲時間
2. **目標網站變更**: 如果目標網站的HTML結構發生變化，可能需要相應調整搜索邏輯
3. **編碼兼容性**: 雖然已處理多種編碼情況，但仍可能遇到特殊編碼需要額外處理

## 相關檔案
- `大專院校爬蟲.py`: 主要爬蟲程序
- `test_csv_download.py`: CSV下載功能測試腳本
- `verify_changes.py`: 代碼修改驗證腳本
