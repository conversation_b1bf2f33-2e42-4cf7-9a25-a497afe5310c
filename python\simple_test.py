#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單測試
"""

import pandas as pd

def test_basic():
    print("測試基本pandas操作...")
    
    # 創建測試DataFrame
    data = {
        'A': ['1,234', '2,345', '3,456'],
        'B': ['test1', 'test2', 'test3']
    }
    df = pd.DataFrame(data)
    print("原始DataFrame:")
    print(df)
    print(f"DataFrame類型: {type(df)}")
    
    # 測試dtype檢查
    print("\n測試dtype檢查:")
    for col in df.columns:
        print(f"列 {col} 的dtype: {df[col].dtype}")
        if df[col].dtype == 'object':
            print(f"  處理列 {col}...")
            df[col] = df[col].astype(str).str.replace(',', '', regex=False)
    
    print("\n處理後的DataFrame:")
    print(df)
    
    print("✅ 基本測試完成")

if __name__ == "__main__":
    test_basic()
