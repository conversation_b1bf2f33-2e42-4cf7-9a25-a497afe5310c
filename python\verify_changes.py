#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證代碼修改是否正確
"""

def 檢查函數定義():
    """檢查函數定義是否正確"""
    print("🔍 檢查函數定義...")
    
    try:
        # 導入修改後的模組
        import 大專院校爬蟲 as crawler
        
        # 檢查函數是否存在
        functions_to_check = [
            '尋找CSV下載連結',
            '下載CSV檔案',
            '處理單一下載任務',
            '主程序'
        ]
        
        for func_name in functions_to_check:
            if hasattr(crawler, func_name):
                print(f"✅ 函數 '{func_name}' 存在")
            else:
                print(f"❌ 函數 '{func_name}' 不存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入模組失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 檢查函數定義時發生錯誤: {e}")
        return False

def 檢查編碼處理():
    """檢查編碼處理邏輯"""
    print("\n🔍 檢查編碼處理邏輯...")
    
    # 測試UTF-8編碼轉換
    test_content = "測試內容,包含中文,123"
    
    try:
        # 模擬UTF-8編碼
        utf8_bytes = test_content.encode('utf-8')
        
        # 模擬解碼過程
        decoded_text = utf8_bytes.decode('utf-8')
        
        # 模擬寫入帶BOM的UTF-8
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8-sig', delete=False, suffix='.csv') as f:
            f.write(decoded_text)
            temp_path = f.name
        
        # 讀取檔案驗證
        with open(temp_path, 'r', encoding='utf-8-sig') as f:
            read_content = f.read()
        
        # 清理臨時檔案
        os.unlink(temp_path)
        
        if read_content == test_content:
            print("✅ UTF-8編碼處理正確")
            return True
        else:
            print("❌ UTF-8編碼處理有問題")
            return False
            
    except Exception as e:
        print(f"❌ 編碼處理測試失敗: {e}")
        return False

def 檢查配置設定():
    """檢查配置設定"""
    print("\n🔍 檢查配置設定...")
    
    try:
        import 大專院校爬蟲 as crawler
        
        # 檢查基本配置
        if hasattr(crawler, 'category_pages'):
            print("✅ category_pages 配置存在")
            
            # 檢查學生類別配置
            if '學生' in crawler.category_pages:
                student_config = crawler.category_pages['學生']
                if 'download_items' in student_config:
                    items = student_config['download_items']
                    print(f"✅ 學生類別有 {len(items)} 個下載項目")
                    for key, value in items.items():
                        print(f"   - {key}: {value}")
                else:
                    print("❌ 學生類別缺少 download_items")
            else:
                print("❌ 缺少學生類別配置")
        else:
            print("❌ category_pages 配置不存在")
            
        return True
        
    except Exception as e:
        print(f"❌ 檢查配置設定時發生錯誤: {e}")
        return False

def 主驗證():
    """主驗證函數"""
    print("🚀 開始驗證代碼修改")
    print("="*50)
    
    results = []
    
    # 1. 檢查函數定義
    results.append(檢查函數定義())
    
    # 2. 檢查編碼處理
    results.append(檢查編碼處理())
    
    # 3. 檢查配置設定
    results.append(檢查配置設定())
    
    # 總結
    print(f"\n{'='*50}")
    success_count = sum(results)
    total_count = len(results)
    
    print(f"📊 驗證結果: {success_count}/{total_count} 項通過")
    
    if success_count == total_count:
        print("🎉 所有驗證項目都通過！代碼修改正確")
    else:
        print("⚠️  部分驗證項目未通過，請檢查代碼")
    
    return success_count == total_count

if __name__ == "__main__":
    主驗證()
