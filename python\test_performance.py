#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試CSV處理性能
"""

import time
import pandas as pd
from pathlib import Path
import tempfile
import os

def 創建測試CSV檔案(file_path, rows=1000):
    """創建測試用的CSV檔案"""
    data = {
        '統計項目': ['測試項目'] * rows,
        '學年度': [111, 112, 113] * (rows // 3 + 1),
        '學校名稱': ['國立雲林科技大學', '國立高雄科技大學', '國立臺灣科技大學', '國立臺北科技大學'] * (rows // 4 + 1),
        '學生數': [f'{i:,}' for i in range(1000, 1000 + rows)],  # 包含千分位逗號
        '比率': [f'{i/10:.1f}%' for i in range(rows)]
    }
    
    # 確保所有列長度一致
    max_len = max(len(v) for v in data.values())
    for key in data:
        while len(data[key]) < max_len:
            data[key].extend(data[key][:max_len - len(data[key])])
        data[key] = data[key][:max_len]
    
    df = pd.DataFrame(data)
    
    # 添加標題行
    header_df = pd.DataFrame([
        ['', '', '', '', ''],  # 第一行空白
        ['', '', '', '', ''],  # 第二行空白
        ['統計項目', '學年度', '學校名稱', '學生數', '比率'],  # 第三行標題
    ])
    
    # 合併標題和數據
    final_df = pd.concat([header_df, df], ignore_index=True)
    
    # 保存為CSV (使用UTF-8-sig編碼)
    final_df.to_csv(file_path, index=False, header=False, encoding='utf-8-sig')
    print(f"✅ 創建測試檔案: {file_path} ({rows} 行數據)")

def 測試處理性能():
    """測試CSV處理性能"""
    print("🚀 開始性能測試")
    print("="*50)
    
    # 創建臨時目錄
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 測試不同大小的檔案
        test_sizes = [100, 500, 1000, 2000]
        
        for size in test_sizes:
            print(f"\n📊 測試 {size} 行數據...")
            
            # 創建測試檔案
            test_file = temp_path / f"test_{size}.csv"
            創建測試CSV檔案(test_file, size)
            
            # 測試讀取性能
            start_time = time.time()
            
            try:
                # 導入優化後的處理函數
                from 資料清理 import 處理CSV檔案
                
                # 處理檔案
                result = 處理CSV檔案(
                    [test_file],
                    title_row_index=2,  # 第三行為標題
                    data_row_index=3,   # 第四行開始為數據
                    filter_data=True
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                if result:
                    total_rows = len(next(iter(result.values())))
                    print(f"✅ 處理完成: {processing_time:.3f} 秒")
                    print(f"   📄 輸入行數: {size}")
                    print(f"   📊 輸出行數: {total_rows}")
                    print(f"   ⚡ 處理速度: {size/processing_time:.0f} 行/秒")
                else:
                    print(f"⚠️  沒有處理到有效數據")
                    
            except Exception as e:
                print(f"❌ 處理失敗: {e}")
    
    print(f"\n{'='*50}")
    print("🎯 性能測試完成")

def 測試記憶體使用():
    """測試記憶體使用情況"""
    print("\n🧠 測試記憶體使用...")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # 記錄初始記憶體
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"📊 初始記憶體使用: {initial_memory:.1f} MB")
        
        # 創建大檔案測試
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            large_file = temp_path / "large_test.csv"
            
            print("📄 創建大型測試檔案 (5000 行)...")
            創建測試CSV檔案(large_file, 5000)
            
            # 記錄檔案創建後的記憶體
            after_create_memory = process.memory_info().rss / 1024 / 1024
            print(f"📊 檔案創建後記憶體: {after_create_memory:.1f} MB")
            
            # 處理檔案
            print("🔄 處理大型檔案...")
            start_time = time.time()
            
            from 資料清理 import 處理CSV檔案
            result = 處理CSV檔案(
                [large_file],
                title_row_index=2,
                data_row_index=3,
                filter_data=True
            )
            
            end_time = time.time()
            
            # 記錄處理後的記憶體
            after_process_memory = process.memory_info().rss / 1024 / 1024
            print(f"📊 處理後記憶體: {after_process_memory:.1f} MB")
            print(f"📈 記憶體增加: {after_process_memory - initial_memory:.1f} MB")
            print(f"⏱️  處理時間: {end_time - start_time:.3f} 秒")
            
            if result:
                total_rows = len(next(iter(result.values())))
                print(f"📊 處理結果: {total_rows} 行")
                
    except ImportError:
        print("⚠️  psutil 未安裝，跳過記憶體測試")
    except Exception as e:
        print(f"❌ 記憶體測試失敗: {e}")

if __name__ == "__main__":
    測試處理性能()
    測試記憶體使用()
