#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試CSV下載功能
"""

import requests
from bs4 import BeautifulSoup
import os
import re

# 測試基本設定
base_url = 'https://udb.moe.edu.tw'
test_url = 'https://udb.moe.edu.tw/udata/DetailReportList/學生類'

def 測試網絡連接():
    """測試網絡連接"""
    try:
        print("🔍 測試網絡連接...")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(test_url, headers=headers, timeout=10)
        print(f"✅ 網絡連接正常，狀態碼: {response.status_code}")
        return response
    except Exception as e:
        print(f"❌ 網絡連接失敗: {e}")
        return None

def 測試CSV連結搜索(response):
    """測試CSV連結搜索"""
    try:
        print("🔍 測試CSV連結搜索...")
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 尋找所有CSV連結
        csv_links = soup.find_all('a', href=lambda href: href and '.csv' in href)
        print(f"✅ 找到 {len(csv_links)} 個CSV下載連結")
        
        if csv_links:
            for i, link in enumerate(csv_links[:3]):  # 只顯示前3個
                href = link.get('href')
                title = link.get('title', '無標題')
                print(f"  {i+1}. {title}")
                print(f"     URL: {href}")
        
        return csv_links
    except Exception as e:
        print(f"❌ CSV連結搜索失敗: {e}")
        return []

def 測試特定目標搜索(soup, target_text):
    """測試特定目標的CSV連結搜索"""
    try:
        print(f"🔍 測試搜索目標: '{target_text}'")
        
        # 尋找包含目標文字的<a>標籤（使用title屬性搜索）
        target_links = soup.find_all('a', title=lambda title: title and target_text in title)
        
        if not target_links:
            print(f"找不到title包含 '{target_text}' 的連結")
            # 如果title搜索失敗，嘗試用文字內容搜索作為備用方案
            target_links = soup.find_all('a', string=lambda text: text and target_text in text.strip())
            if not target_links:
                print(f"找不到包含 '{target_text}' 的連結")
                return None
            else:
                print(f"使用文字內容搜索找到連結")
        else:
            print(f"使用title屬性搜索找到連結")

        # 找到目標連結後，尋找同一行的CSV下載按鈕
        for link in target_links:
            # 找到包含此連結的<tr>元素
            tr_element = link.find_parent('tr')
            if tr_element:
                # 在這個<tr>中尋找CSV下載連結
                csv_link = tr_element.find('a', href=lambda href: href and '.csv' in href)
                if csv_link:
                    csv_url = csv_link.get('href')
                    # 如果是相對路徑，轉換為絕對路徑
                    if csv_url.startswith('/'):
                        csv_url = base_url + csv_url
                    print(f"✅ 找到CSV下載連結: {csv_url}")
                    return csv_url

        print(f"找到 '{target_text}' 連結，但找不到對應的CSV下載連結")
        return None
        
    except Exception as e:
        print(f"❌ 特定目標搜索失敗: {e}")
        return None

def 主測試():
    """主測試函數"""
    print("🚀 開始測試CSV下載功能")
    print("="*50)
    
    # 1. 測試網絡連接
    response = 測試網絡連接()
    if not response:
        return
    
    # 2. 測試CSV連結搜索
    soup = BeautifulSoup(response.text, 'html.parser')
    csv_links = 測試CSV連結搜索(response)
    
    # 3. 測試特定目標搜索
    test_targets = [
        '學1-1.正式學籍在學學生人數-以「系(所)」統計',
        '學3-2.外國學生數及其在學比率-以「系(所)」統計'
    ]
    
    for target in test_targets:
        print(f"\n{'-'*30}")
        csv_url = 測試特定目標搜索(soup, target)
        if csv_url:
            print(f"✅ 成功找到 '{target}' 的CSV下載連結")
        else:
            print(f"❌ 未找到 '{target}' 的CSV下載連結")
    
    print(f"\n{'='*50}")
    print("🎯 測試完成")

if __name__ == "__main__":
    主測試()
