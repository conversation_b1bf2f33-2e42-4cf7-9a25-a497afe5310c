#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的CSV處理功能
"""

import pandas as pd
from pathlib import Path
import tempfile

def 創建測試CSV():
    """創建一個簡單的測試CSV檔案"""
    # 創建測試數據
    data = [
        ['', '', '', ''],  # 第1行空白
        ['', '', '', ''],  # 第2行空白  
        ['學年度', '學校名稱', '學生數', '比率'],  # 第3行標題
        ['111', '國立雲林科技大學', '1,234', '12.5%'],  # 第4行數據
        ['112', '國立高雄科技大學', '2,345', '23.4%'],  # 第5行數據
        ['113', '國立臺灣科技大學', '3,456', '34.5%'],  # 第6行數據
        ['統計說明', '', '', ''],  # 統計說明行
        ['備註', '', '', '']  # 備註行
    ]
    
    # 創建臨時檔案
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
        for row in data:
            f.write(','.join(row) + '\n')
        temp_file = f.name
    
    return Path(temp_file)

def 測試CSV處理():
    """測試CSV處理功能"""
    print("🧪 開始測試CSV處理功能")
    print("="*40)
    
    # 創建測試檔案
    test_file = 創建測試CSV()
    print(f"📄 創建測試檔案: {test_file}")
    
    try:
        # 導入處理函數
        from 資料清理 import 處理CSV檔案
        
        print("🔄 開始處理CSV檔案...")
        
        # 處理檔案
        result = 處理CSV檔案(
            [test_file],
            title_row_index=2,  # 第3行為標題
            data_row_index=3,   # 第4行開始為數據
            filter_data=False   # 暫時關閉過濾以測試基本功能
        )
        
        if result:
            print("✅ 處理成功！")
            print("📊 處理結果:")
            for key, values in result.items():
                print(f"  {key}: {values}")
        else:
            print("❌ 處理失敗，沒有返回數據")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理臨時檔案
        try:
            test_file.unlink()
            print(f"🗑️  清理臨時檔案: {test_file}")
        except:
            pass
    
    print("🎯 測試完成")

if __name__ == "__main__":
    測試CSV處理()
